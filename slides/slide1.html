<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Professional Software Projects Showcase</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

    body {
      margin: 0;
      padding: 0;
      width: 100vw;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #e0e0e0;
      font-family: 'Inter', sans-serif;
    }

    .slide {
      width: 1280px;
      height: 720px;
      background-color: #FDFCF9;
      position: relative;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: center;
      box-shadow: 0 10px 30px rgba(45, 24, 16, 0.15);
    }
    
    .slide-content {
        z-index: 2;
        padding: 40px;
        animation: fadeInContent 1.2s ease-in-out forwards;
        opacity: 0;
    }
    
    @keyframes fadeInContent {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    h1 {
      font-size: 72px;
      font-weight: 700;
      color: #2D1810;
      margin: 0 0 20px 0;
      letter-spacing: -1px;
      text-shadow: 1px 1px 3px rgba(160, 129, 106, 0.1);
    }

    h2 {
      font-size: 48px;
      font-weight: 600;
      margin: 0 0 25px 0;
      background: linear-gradient(90deg, #E07B39, #A0816A);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }

    p.tagline {
      font-size: 24px;
      color: #5D4E42;
      margin: 0;
      max-width: 800px;
      line-height: 1.5;
    }

    .footer {
      position: absolute;
      bottom: 40px;
      left: 60px;
      right: 60px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      z-index: 2;
      animation: fadeInContent 1.2s ease-in-out 0.2s forwards;
      opacity: 0;
    }

    .presenter {
      font-size: 18px;
      font-weight: 600;
      color: #2D1810;
    }

    .date {
      font-size: 18px;
      color: #5D4E42;
    }
    
    .accent-shape {
        position: absolute;
        border-radius: 50%;
        opacity: 0;
        animation: fadeInShape 2s ease-out forwards;
    }
    
    @keyframes fadeInShape {
        from {
            opacity: 0;
            transform: scale(0.8);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    .shape1 {
        width: 300px;
        height: 300px;
        background: radial-gradient(circle, rgba(224, 123, 57, 0.15) 0%, rgba(224, 123, 57, 0) 70%);
        top: -100px;
        left: -100px;
        animation-delay: 0.2s;
    }
    
    .shape2 {
        width: 400px;
        height: 400px;
        background: radial-gradient(circle, rgba(160, 129, 106, 0.1) 0%, rgba(160, 129, 106, 0) 70%);
        bottom: -150px;
        right: -150px;
        animation-delay: 0.4s;
    }
    
    .shape3 {
        width: 200px;
        height: 200px;
        background: radial-gradient(circle, rgba(224, 123, 57, 0.1) 0%, rgba(224, 123, 57, 0) 70%);
        bottom: 50px;
        left: 100px;
        animation-delay: 0.6s;
    }

  </style>
</head>
<body>
  <div class="slide">
    <div class="accent-shape shape1"></div>
    <div class="accent-shape shape2"></div>
    <div class="accent-shape shape3"></div>
    <div class="slide-content">
      <h2>openai-proxy</h2>
      <p class="tagline">OpenAI-Compatible Proxy Solutions</p>
    </div>
    <div class="footer">
      <div class="presenter">Xiaolin Liu</div>
      <div class="date">September 26, 2025</div>
    </div>
  </div>
</body>
</html>