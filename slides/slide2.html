<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Agenda Slide</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap');

    body {
      margin: 0;
      padding: 0;
      width: 100vw;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f0f0f0;
      font-family: 'Inter', 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    }

    .slide {
      width: 1280px;
      height: 720px;
      background-color: #FDFCF9;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 90px 120px;
      box-sizing: border-box;
      position: relative;
      overflow: hidden;
      animation: fadeIn 1s ease-in-out;
    }

    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    .slide::before {
        content: '';
        position: absolute;
        bottom: -200px;
        right: -200px;
        width: 500px;
        height: 500px;
        background: radial-gradient(circle, rgba(224, 123, 57, 0.1), rgba(224, 123, 57, 0));
        border-radius: 50%;
        pointer-events: none;
    }
    
    .slide::after {
        content: '';
        position: absolute;
        top: -150px;
        left: -150px;
        width: 400px;
        height: 400px;
        background: radial-gradient(circle, rgba(224, 123, 57, 0.08), rgba(224, 123, 57, 0));
        border-radius: 50%;
        pointer-events: none;
    }

    h1 {
      color: #2D1810;
      font-size: 64px;
      font-weight: 700;
      margin: 0 0 60px 0;
      padding: 0;
      line-height: 1.2;
      animation: slideInUp 0.6s ease-out;
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    li {
      color: #5D4E42;
      font-size: 30px;
      line-height: 1.6;
      margin-bottom: 24px;
      padding-left: 55px;
      position: relative;
      opacity: 0;
      animation: slideInUp 0.5s ease-out forwards;
    }
    
    li:nth-child(1) { animation-delay: 0.2s; }
    li:nth-child(2) { animation-delay: 0.3s; }
    li:nth-child(3) { animation-delay: 0.4s; }
    li:nth-child(4) { animation-delay: 0.5s; }
    li:nth-child(5) { animation-delay: 0.6s; }
    li:nth-child(6) { animation-delay: 0.7s; }


    li::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 12px;
      height: 28px;
      background-color: #E07B39;
      border-radius: 4px;
    }
    
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

  </style>
</head>
<body>
  <div class="slide">
    <h1>Today's Overview</h1>
    <ul>
      <li>Introduction to codex-cli: Orange Dogfood SWE Agent Solution</li>
      <li>codex-cli Key Features & Architecture</li>
      <li>Introduction to openai-proxy: FastAPI-Based Local Model Proxy</li>
      <li>openai-proxy Architecture & Usage Examples</li>
      <li>Project Relationship & Comparison</li>
      <li>Summary & Next Steps</li>
    </ul>
  </div>
</body>
</html>